<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Table already exists in production - only ensure missing columns are added
        Schema::table('eifu_lot_numbers_software', function (Blueprint $table) {
            if (!Schema::hasColumn('eifu_lot_numbers_software', 'deleted_at')) {
                $table->softDeletes();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Do not drop the table as it contains production data
        // Only remove the deleted_at column if it was added by this migration
        Schema::table('eifu_lot_numbers_software', function (Blueprint $table) {
            if (Schema::hasColumn('eifu_lot_numbers_software', 'deleted_at')) {
                $table->dropSoftDeletes();
            }
        });
    }
};
